{"name": "better-auth-next-app-starter", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --ignore-path .gitignore \"src/**/*.+(ts|js|tsx)\" --write", "check-types": "tsc --noEmit"}, "dependencies": {"@daveyplate/better-auth-ui": "^2.0.8", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tabler/icons-react": "^3.34.1", "@tanstack/react-table": "^8.21.3", "better-auth": "1.2.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "drizzle-orm": "^0.44.2", "lucide-react": "^0.518.0", "next": "15.3.4", "next-themes": "0.4.6", "pg": "^8.16.2", "react": "^19.1.0", "react-dom": "^19.1.0", "recharts": "2.15.4", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "vaul": "^1.1.2", "zod": "^4.0.15"}, "devDependencies": {"@better-auth/cli": "^1.2.9", "@tailwindcss/postcss": "^4.1.10", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/node": "^24.0.3", "@types/pg": "^8.15.4", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "dotenv": "^16.5.0", "drizzle-kit": "^0.31.1", "esbuild": "^0.25.5", "eslint": "^9", "eslint-config-next": "15.4.6", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4.1.10", "tailwindcss-safe-area": "^0.6.0", "turbo": "^2.5.4", "tw-animate-css": "^1.3.4", "typescript": "^5.8.3"}, "pnpm": {"overrides": {}}, "packageManager": "pnpm@10.12.1"}