import { betterAuth } from 'better-auth'
import { drizzleAdapter } from 'better-auth/adapters/drizzle'

import { db } from '@/database/db'
import * as schema from '@/database/schema'

export const auth = betterAuth({
	database: drizzleAdapter(db, {
		provider: 'pg',
		usePlural: true,
		schema,
	}),
	emailAndPassword: {
		enabled: true,
	},
	user: {
		additionalFields: {
			country: {
				type: "string",
				required: true,
			},
			city: {
				type: "string",
				required: true,
			},
		},
	},
})
