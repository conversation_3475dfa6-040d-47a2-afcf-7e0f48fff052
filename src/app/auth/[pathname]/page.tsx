import { AuthCard } from '@daveyplate/better-auth-ui'
import { authViewPaths } from '@daveyplate/better-auth-ui/server'
import { GalleryVerticalEnd } from 'lucide-react'
import { headers } from 'next/headers'
import Image from 'next/image'
import Link from 'next/link'
import { redirect } from 'next/navigation'

import { auth } from '@/lib/auth'

export function generateStaticParams() {
	return Object.values(authViewPaths).map((pathname) => ({ pathname }))
}

export default async function AuthPage({ params }: { params: Promise<{ pathname: string }> }) {
	const { pathname } = await params

	// **EXAMPLE** SSR route protection for /auth/settings
	// NOTE: This opts /auth/settings out of static rendering
	// It already handles client side protection via useAuthenticate
	if (pathname === 'settings') {
		const sessionData = await auth.api.getSession({
			headers: await headers(),
		})

		if (!sessionData) redirect('/auth/sign-in?redirectTo=/auth/settings')
	}

	// Use two-column layout for auth forms, single column for settings
	const isAuthForm = ['sign-in', 'sign-up', 'magic-link', 'forgot-password'].includes(pathname)

	if (isAuthForm) {
		return (
			<div className="grid min-h-svh lg:grid-cols-2">
				<div className="flex flex-col gap-4 p-6 md:p-10">
					<div className="flex justify-center gap-2 md:justify-start">
						<Link href="/" className="flex items-center gap-2 font-medium">
							<div className="flex size-6 items-center justify-center rounded-md bg-primary text-primary-foreground">
								<GalleryVerticalEnd className="size-4" />
							</div>
							SnowHelpers.com
						</Link>
					</div>
					<div className="flex flex-1 items-center justify-center">
						<div className="w-full max-w-md">
							<AuthCard
								pathname={pathname}
								redirectTo="/dashboard"
								className="border-none shadow-none"
								classNames={{
									base: 'shadow-none border-none',
									form: {
										base: 'shadow-none',
										input: 'shadow-none',
									},
								}}
							/>
						</div>
					</div>
				</div>
				<div className="relative hidden bg-muted lg:block">
					<Image
						src="/placeholder.svg"
						alt="Authentication"
						fill
						className="object-cover dark:brightness-[0.2] dark:grayscale"
					/>
				</div>
			</div>
		)
	}

	// Settings page uses the original layout
	return (
		<main className="container flex grow flex-col items-center justify-center gap-4 self-center p-4 md:p-6">
			<AuthCard
				classNames={{
					settings: {
						sidebar: {
							base: 'sticky top-20',
						},
					},
				}}
				pathname={pathname}
			/>
		</main>
	)
}
