import type { <PERSON>ada<PERSON>, Viewport } from 'next'
import { <PERSON><PERSON><PERSON>, <PERSON>eist_Mono } from 'next/font/google'
import type { ReactNode } from 'react'

import { Providers } from './providers'

import '@/styles/globals.css'

const geistSans = Geist({
	variable: '--font-geist-sans',
	subsets: ['latin'],
})

const geistMono = Geist_Mono({
	variable: '--font-geist-mono',
	subsets: ['latin'],
})

export const metadata: Metadata = {
	title: 'SnowHelpers.com',
	description: 'Your trusted snow removal and winter services platform',
}

export const viewport: Viewport = {
	initialScale: 1,
	viewportFit: 'cover',
	width: 'device-width',
}

export default function RootLayout({
	children,
}: Readonly<{
	children: ReactNode
}>) {
	return (
		<html lang="en" suppressHydrationWarning>
			<body
				className={`${geistSans.variable} ${geistMono.variable} flex min-h-svh flex-col antialiased`}
			>
				<Providers>{children}</Providers>
			</body>
		</html>
	)
}
