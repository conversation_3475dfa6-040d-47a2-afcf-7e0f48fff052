'use client'

import { Auth<PERSON><PERSON>rovider } from '@daveyplate/better-auth-ui'
import { ThemeProvider } from 'next-themes'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import type { ReactNode } from 'react'
import { Toaster } from 'sonner'

import { authClient } from '@/lib/auth-client'

export function Providers({ children }: { children: ReactNode }) {
	const router = useRouter()

	return (
		<ThemeProvider
			attribute="class"
			defaultTheme="light"
			enableSystem
			disableTransitionOnChange
		>
			<AuthUIProvider
				authClient={authClient}
				navigate={router.push}
				replace={router.replace}
				onSessionChange={() => {
					router.refresh()
				}}
				settings={{
					url: '/dashboard/settings',
				}}
				signUp={{
					fields: ['country', 'city'],
				}}
				Link={Link}
				additionalFields={{
					country: {
						label: 'Country',
						placeholder: 'Enter "Canada" or "United States"',
						description: 'Please enter either "Canada" or "United States"',
						type: 'string',
						required: true,
						validate: async (value: string) => {
							const validCountries = ['Canada', 'United States']
							return validCountries.includes(value)
						},
					},
					city: {
						label: 'City',
						placeholder: 'Enter your city',
						type: 'string',
						required: true,
					},
				}}
			>
				{children}

				<Toaster />
			</AuthUIProvider>
		</ThemeProvider>
	)
}
