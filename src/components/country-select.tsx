'use client'

import * as React from 'react'
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select'

const countries = [
	{ label: 'Canada', value: 'Canada' },
	{ label: 'United States', value: 'United States' },
]

interface CountrySelectProps {
	value?: string
	onValueChange?: (value: string) => void
	placeholder?: string
	className?: string
}

export function CountrySelect({ value, onValueChange, placeholder = 'Select country...', className }: CountrySelectProps) {
	return (
		<Select value={value} onValueChange={onValueChange}>
			<SelectTrigger className={className}>
				<SelectValue placeholder={placeholder} />
			</SelectTrigger>
			<SelectContent>
				{countries.map((country) => (
					<SelectItem key={country.value} value={country.value}>
						{country.label}
					</SelectItem>
				))}
			</SelectContent>
		</Select>
	)
}
